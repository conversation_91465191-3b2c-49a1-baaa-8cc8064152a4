# IoT 系统集成模块

本模块实现了 FastAPI Best Architecture 与 TS-IOT-SYS 系统的身份认证和授权集成。

## 🎯 功能特性

- ✅ **JWT Token 验证**：支持验证来自 TS-IOT-SYS 的 JWT token
- ✅ **Redis 缓存集成**：与 Java 系统共享 Redis 存储
- ✅ **RBAC 权限控制**：基于角色的访问控制
- ✅ **权限缓存**：高性能的权限验证
- ✅ **适配器模式**：无缝集成现有认证系统
- ✅ **向后兼容**：不影响现有 FBA 认证功能
- ✅ **外部服务集成**：支持调用外部知识库服务（RAGFlow）
- ✅ **代理模式**：作为认证代理转发请求到外部服务

## 📁 目录结构

```
backend/app/iot/
├── __init__.py                 # 模块初始化
├── README.md                   # 本文档
├── config_example.py           # 配置示例
├── api/                        # API 接口
│   ├── __init__.py
│   └── v1/
│       ├── __init__.py
│       ├── router.py           # 路由注册
│       ├── auth.py             # 认证相关接口
│       └── device.py           # 设备管理接口
├── schema/                     # 数据模型
│   ├── __init__.py
│   └── device.py               # 设备相关 Schema
├── service/                    # 业务逻辑
│   ├── __init__.py
│   └── device_service.py       # 设备服务
└── tests/                      # 测试用例
    ├── __init__.py
    └── test_iot_auth.py        # 认证测试
```

## 🚀 快速开始

### 1. 配置环境变量

在 `.env` 文件中添加以下配置：

```bash
# IoT 系统集成配置
IOT_INTEGRATION_ENABLED=true
IOT_JWT_SECRET_KEY=your-shared-secret-key-with-java-system
IOT_JWT_ALGORITHM=HS256
IOT_JWT_ISSUER=TS-IOT-SYS
IOT_TOKEN_PREFIX=login_tokens:
IOT_USER_PREFIX=user_info:
IOT_PERMISSION_PREFIX=user_permissions:
IOT_USER_CACHE_TTL=300
IOT_PERMISSION_CACHE_TTL=600
IOT_DEBUG_MODE=false

# 外部知识库服务配置
KNOWLEDGE_BASE_URL=http://*************:6610
KNOWLEDGE_BASE_API_KEY=ragflow-c2ZmY3MzVhMDkwYzExZjA5MzIwZjZjZW
KNOWLEDGE_BASE_TIMEOUT=30.0
```

### 2. 注册路由

在主应用中注册 IoT 路由（需要在 `backend/main.py` 或相应的路由注册文件中添加）：

```python
from backend.app.iot.api.v1.router import iot_v1_router

app.include_router(iot_v1_router)
```

### 3. 验证配置

访问健康检查接口验证配置：

```bash
curl -X GET "http://localhost:8000/api/v1/iot/auth/health"
```

### 4. 测试认证

使用 IoT 系统的 JWT token 测试认证：

```bash
curl -X GET "http://localhost:8000/api/v1/iot/auth/test" \
     -H "Authorization: Bearer <your-iot-jwt-token>"
```

## 🔐 权限配置

### 权限标识格式

IoT 权限采用三段式格式：`模块:资源:操作`

```
iot:device:list      # 设备列表查看
iot:device:view      # 设备详情查看
iot:device:control   # 设备控制
iot:data:view        # 数据查看
iot:user:manage      # 用户管理
```

### 通配符权限

支持通配符权限配置：

```
iot:device:*         # 设备所有权限
iot:*:view           # 所有查看权限
*:*:*                # 超级管理员权限
```

### 权限配置步骤

1. 在系统菜单中添加 IoT 权限标识
2. 为用户角色分配相应的菜单权限
3. 用户通过角色继承权限

## 🔧 API 接口

### 认证相关

- `GET /api/v1/iot/auth/health` - 健康检查
- `GET /api/v1/iot/auth/test` - 认证测试
- `GET /api/v1/iot/auth/user/info` - 获取用户信息
- `GET /api/v1/iot/auth/permissions/test` - 权限测试

### 设备管理

- `GET /api/v1/iot/device/list` - 获取设备列表
- `GET /api/v1/iot/device/{device_id}` - 获取设备详情
- `POST /api/v1/iot/device/control/{device_id}` - 控制设备
- `GET /api/v1/iot/device/data/{device_id}` - 获取设备数据
- `GET /api/v1/iot/device/statistics/summary` - 获取统计摘要

## 🧪 测试

运行测试用例：

```bash
# 运行 IoT 认证测试
python -m pytest backend/app/iot/tests/test_iot_auth.py -v

# 运行所有测试
python -m pytest backend/app/iot/tests/ -v
```

## 🔍 调试

### 启用调试模式

设置环境变量：

```bash
IOT_DEBUG_MODE=true
```

### 查看日志

调试模式下会输出详细的认证和权限验证日志。

### 常见问题

1. **认证失败**：检查 JWT 密钥是否与 Java 系统一致
2. **权限验证失败**：确认用户角色是否分配了相应权限
3. **配置问题**：使用健康检查接口验证配置状态

## 📚 扩展开发

### 添加新的 API 接口

1. 在 `backend/app/iot/api/v1/` 下创建新的路由文件
2. 在 `backend/app/iot/api/v1/router.py` 中注册路由
3. 使用 IoT 权限装饰器进行权限控制

### 添加新的权限

1. 在 `backend/common/security/iot_permission.py` 的 `IoTPermissions` 类中添加权限常量
2. 在系统菜单中配置相应的权限标识
3. 在 API 接口中使用新的权限进行控制

### 自定义认证逻辑

可以扩展 `backend/common/security/iot_adapter.py` 中的 `IoTAuthAdapter` 类来实现自定义的认证逻辑。

## 📄 许可证

本模块遵循 FastAPI Best Architecture 项目的许可证。
