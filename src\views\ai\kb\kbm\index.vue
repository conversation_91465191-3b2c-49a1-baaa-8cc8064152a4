<template>
  <div class="knowledge-base-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">
          <el-icon><Collection /></el-icon>
          知识库管理
        </h2>
        <p class="page-description">管理您的AI知识库，支持创建、编辑、删除等操作</p>
      </div>
      <div class="header-right">
        <el-button
          type="primary"
          @click="handleCreate"
          :icon="Plus"
        >
          创建知识库
        </el-button>
        <el-button @click="testConnection" :loading="testLoading">
          测试连接
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards" v-if="stats" v-auth="'iot:kb:stats'">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#409EFF"><DataBoard /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.total_kb }}</div>
                <div class="stats-label">知识库总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#67C23A"><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.total_documents }}</div>
                <div class="stats-label">文档总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#E6A23C"><Grid /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.total_chunks }}</div>
                <div class="stats-label">分块总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon">
                <el-icon color="#F56C6C"><Coin /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ formatNumber(stats.total_tokens) }}</div>
                <div class="stats-label">Token总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="search-bar">
      <div class="search-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索知识库名称"
          :prefix-icon="Search"
          clearable
          @input="handleSearch"
          style="width: 300px"
        />
        <el-select v-model="queryParams.orderby" @change="loadKnowledgeBases" style="width: 120px; margin-left: 10px">
          <el-option label="创建时间" value="create_time" />
          <el-option label="更新时间" value="update_time" />
        </el-select>
        <el-select v-model="queryParams.desc" @change="loadKnowledgeBases" style="width: 100px; margin-left: 10px">
          <el-option label="降序" :value="true" />
          <el-option label="升序" :value="false" />
        </el-select>
      </div>
      <div class="search-right">
        <el-button @click="loadKnowledgeBases" :icon="Refresh">刷新</el-button>
        <el-button
          v-auth="'iot:kb:delete'"
          type="danger"
          @click="handleBatchDelete"
          :disabled="selectedKbs.length === 0"
          :icon="Delete"
        >
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 知识库列表 -->
    <div class="kb-list">
      <el-table
        v-loading="loading"
        :data="knowledgeBases"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="知识库名称" min-width="200">
          <template #default="{ row }">
            <div class="kb-name-cell">
              <el-avatar :size="40" :src="row.avatar" class="kb-avatar">
                <el-icon><Collection /></el-icon>
              </el-avatar>
              <div class="kb-info">
                <div class="kb-name">{{ row.name }}</div>
                <div class="kb-description">{{ row.description || '暂无描述' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="embedding_model" label="嵌入模型" width="200">
          <template #default="{ row }">
            <el-tag size="small">{{ getModelDisplayName(row.embedding_model) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="chunk_method" label="分块方法" width="120">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ getChunkMethodName(row.chunk_method) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="统计信息" width="180">
          <template #default="{ row }">
            <div class="stats-cell">
              <div class="stat-item">
                <span class="stat-label">文档:</span>
                <span class="stat-value">{{ row.document_count }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">分块:</span>
                <span class="stat-value">{{ row.chunk_count }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Token:</span>
                <span class="stat-value">{{ formatNumber(row.token_num) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="permission" label="权限" width="80">
          <template #default="{ row }">
            <el-tag :type="row.permission === 'me' ? 'warning' : 'success'" size="small">
              {{ row.permission === 'me' ? '私有' : '团队' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === '1' ? 'success' : 'danger'" size="small">
              {{ row.status === '1' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_date" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.create_date) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              v-auth="'iot:kb:view'"
              size="small"
              @click="handleView(row)"
              :icon="View"
            >
              查看
            </el-button>
            <el-button
              v-auth="'iot:kb:edit'"
              size="small"
              type="primary"
              @click="handleEdit(row)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-button
              v-auth="'iot:kb:delete'"
              size="small"
              type="danger"
              @click="handleDelete(row)"
              :icon="Delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.page_size"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>

  <!-- 创建/编辑知识库对话框 -->
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    @close="resetForm"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="知识库名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入知识库名称" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入知识库描述"
        />
      </el-form-item>
      <el-form-item label="嵌入模型" prop="embedding_model">
        <el-select v-model="formData.embedding_model" placeholder="选择嵌入模型" style="width: 100%">
          <el-option
            v-for="model in embeddingModels"
            :key="model.value"
            :label="model.label"
            :value="model.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分块方法" prop="chunk_method">
        <el-select v-model="formData.chunk_method" placeholder="选择分块方法" style="width: 100%">
          <el-option
            v-for="method in chunkMethods"
            :key="method.value"
            :label="method.label"
            :value="method.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="访问权限" prop="permission">
        <el-radio-group v-model="formData.permission">
          <el-radio value="me">私有</el-radio>
          <el-radio value="team">团队</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="页面排名" prop="pagerank">
        <el-slider v-model="formData.pagerank" :min="0" :max="100" show-input />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 查看知识库详情对话框 -->
  <el-dialog
    v-model="detailDialogVisible"
    title="知识库详情"
    width="800px"
  >
    <div v-if="currentKb" class="kb-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="知识库名称">{{ currentKb.name }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="currentKb.status === '1' ? 'success' : 'danger'">
            {{ currentKb.status === '1' ? '正常' : '停用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ currentKb.description || '暂无描述' }}</el-descriptions-item>
        <el-descriptions-item label="嵌入模型">{{ getModelDisplayName(currentKb.embedding_model) }}</el-descriptions-item>
        <el-descriptions-item label="分块方法">{{ getChunkMethodName(currentKb.chunk_method) }}</el-descriptions-item>
        <el-descriptions-item label="访问权限">
          <el-tag :type="currentKb.permission === 'me' ? 'warning' : 'success'">
            {{ currentKb.permission === 'me' ? '私有' : '团队' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="页面排名">{{ currentKb.pagerank }}</el-descriptions-item>
        <el-descriptions-item label="文档数量">{{ currentKb.document_count }}</el-descriptions-item>
        <el-descriptions-item label="分块数量">{{ currentKb.chunk_count }}</el-descriptions-item>
        <el-descriptions-item label="Token数量">{{ formatNumber(currentKb.token_num) }}</el-descriptions-item>
        <el-descriptions-item label="相似度阈值">{{ currentKb.similarity_threshold }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(currentKb.create_date) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDate(currentKb.update_date) }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox, FormInstance, FormRules } from 'element-plus';
import {
  Plus,
  Search,
  Refresh,
  Delete,
  Edit,
  View,
  Collection,
  DataBoard,
  Document,
  Grid,
  Coin
} from '@element-plus/icons-vue';
import {
  getKnowledgeBaseList,
  createKnowledgeBase,
  updateKnowledgeBase,
  deleteKnowledgeBases,
  getKnowledgeBaseStats,
  getKnowledgeBaseDetail,
  getEmbeddingModels,
  getChunkMethods,
  checkKnowledgeBaseHealth,
  type KnowledgeBase,
  type KnowledgeBaseStats,
  type CreateKnowledgeBaseParams,
  type UpdateKnowledgeBaseParams,
  type KnowledgeBaseQueryParams
} from '/@/api/iot/knowledgeBase';

// 响应式数据
const loading = ref(false);
const submitting = ref(false);
const testLoading = ref(false);
const knowledgeBases = ref<KnowledgeBase[]>([]);
const selectedKbs = ref<KnowledgeBase[]>([]);
const stats = ref<KnowledgeBaseStats | null>(null);
const total = ref(0);
const searchKeyword = ref('');

// 对话框相关
const dialogVisible = ref(false);
const detailDialogVisible = ref(false);
const isEdit = ref(false);
const currentKb = ref<KnowledgeBase | null>(null);

// 表单相关
const formRef = ref<FormInstance>();
const formData = reactive<CreateKnowledgeBaseParams>({
  name: '',
  description: '',
  embedding_model: 'BAAI/bge-large-zh-v1.5@BAAI',
  permission: 'me',
  chunk_method: 'naive',
  pagerank: 0,
  parser_config: {
    chunk_token_num: 128,
    delimiter: '\\n!?;。；！？',
    html4excel: false,
    layout_recognize: 'DeepDOC',
    auto_keywords: 0,
    auto_questions: 0,
    task_page_size: 12
  }
});

// 查询参数
const queryParams = reactive<KnowledgeBaseQueryParams>({
  page: 1,
  page_size: 30,
  orderby: 'create_time',
  desc: true
});

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { min: 1, max: 128, message: '名称长度在 1 到 128 个字符', trigger: 'blur' }
  ],
  embedding_model: [
    { required: true, message: '请选择嵌入模型', trigger: 'change' }
  ],
  chunk_method: [
    { required: true, message: '请选择分块方法', trigger: 'change' }
  ],
  permission: [
    { required: true, message: '请选择访问权限', trigger: 'change' }
  ]
};

// 嵌入模型和分块方法选项
const embeddingModels = ref<Array<{ label: string; value: string }>>([]);
const chunkMethods = ref<Array<{ label: string; value: string }>>([]);

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑知识库' : '创建知识库');

// 生命周期
onMounted(() => {
  loadKnowledgeBases();
  loadStats();
  loadOptions();
});

// 方法定义
const loadKnowledgeBases = async () => {
  try {
    loading.value = true;
    const params = { ...queryParams };
    if (searchKeyword.value) {
      params.name = searchKeyword.value;
    }

    const response = await getKnowledgeBaseList(params);
    if (response.code === 0 && response.data) {
      knowledgeBases.value = response.data;
      // 这里应该从响应中获取总数，暂时使用数据长度
      total.value = response.data.length;
    } else {
      ElMessage.error(response.message || '获取知识库列表失败');
    }
  } catch (error) {
    console.error('加载知识库列表失败:', error);
    ElMessage.error('加载知识库列表失败');
  } finally {
    loading.value = false;
  }
};

const loadStats = async () => {
  try {
    const response = await getKnowledgeBaseStats();
    if (response.code === 0 && response.data) {
      stats.value = response.data;
    }
  } catch (error) {
    console.error('加载统计信息失败:', error);
  }
};

const loadOptions = async () => {
  try {
    embeddingModels.value = await getEmbeddingModels();
    chunkMethods.value = await getChunkMethods();
  } catch (error) {
    console.error('加载选项失败:', error);
  }
};

const handleCreate = () => {
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

const handleEdit = (row: KnowledgeBase) => {
  isEdit.value = true;
  currentKb.value = row;

  // 填充表单数据
  Object.assign(formData, {
    name: row.name,
    description: row.description,
    embedding_model: row.embedding_model,
    permission: row.permission,
    chunk_method: row.chunk_method,
    pagerank: row.pagerank
  });

  dialogVisible.value = true;
};

const handleView = async (row: KnowledgeBase) => {
  try {
    const response = await getKnowledgeBaseDetail(row.id!);
    if (response.code === 0 && response.data) {
      currentKb.value = response.data;
      detailDialogVisible.value = true;
    } else {
      ElMessage.error(response.message || '获取知识库详情失败');
    }
  } catch (error) {
    console.error('获取知识库详情失败:', error);
    ElMessage.error('获取知识库详情失败');
  }
};

const handleDelete = async (row: KnowledgeBase) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除知识库 "${row.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const response = await deleteKnowledgeBases([row.id!]);
    if (response.code === 0) {
      ElMessage.success('删除成功');
      loadKnowledgeBases();
      loadStats();
    } else {
      ElMessage.error(response.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除知识库失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

const handleBatchDelete = async () => {
  if (selectedKbs.value.length === 0) {
    ElMessage.warning('请选择要删除的知识库');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedKbs.value.length} 个知识库吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const ids = selectedKbs.value.map(kb => kb.id!);
    const response = await deleteKnowledgeBases(ids);
    if (response.code === 0) {
      ElMessage.success('批量删除成功');
      selectedKbs.value = [];
      loadKnowledgeBases();
      loadStats();
    } else {
      ElMessage.error(response.message || '批量删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error);
      ElMessage.error('批量删除失败');
    }
  }
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    let response;
    if (isEdit.value && currentKb.value) {
      const updateData: UpdateKnowledgeBaseParams = {
        name: formData.name,
        description: formData.description,
        embedding_model: formData.embedding_model,
        permission: formData.permission,
        chunk_method: formData.chunk_method,
        pagerank: formData.pagerank
      };
      response = await updateKnowledgeBase(currentKb.value.id!, updateData);
    } else {
      response = await createKnowledgeBase(formData);
    }

    if (response.code === 0) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功');
      dialogVisible.value = false;
      loadKnowledgeBases();
      loadStats();
    } else {
      ElMessage.error(response.message || (isEdit.value ? '更新失败' : '创建失败'));
    }
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error('提交失败');
  } finally {
    submitting.value = false;
  }
};

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(formData, {
    name: '',
    description: '',
    embedding_model: 'BAAI/bge-large-zh-v1.5@BAAI',
    permission: 'me',
    chunk_method: 'naive',
    pagerank: 0,
    parser_config: {
      chunk_token_num: 128,
      delimiter: '\\n!?;。；！？',
      html4excel: false,
      layout_recognize: 'DeepDOC',
      auto_keywords: 0,
      auto_questions: 0,
      task_page_size: 12
    }
  });
  currentKb.value = null;
};

const handleSearch = () => {
  queryParams.page = 1;
  loadKnowledgeBases();
};

const handleSelectionChange = (selection: KnowledgeBase[]) => {
  selectedKbs.value = selection;
};

const handleSizeChange = (size: number) => {
  queryParams.page_size = size;
  queryParams.page = 1;
  loadKnowledgeBases();
};

const handleCurrentChange = (page: number) => {
  queryParams.page = page;
  loadKnowledgeBases();
};

// 测试连接
const testConnection = async () => {
  testLoading.value = true;
  try {
    const result = await checkKnowledgeBaseHealth();
    console.log('连接测试结果:', result);

    if (result.code === 0) {
      ElMessage.success(`连接测试成功: ${result.data?.message || '服务正常'}`);
    } else {
      ElMessage.warning(`连接测试警告: ${result.message || '服务状态异常'}`);
    }
  } catch (error) {
    console.error('连接测试失败:', error);
    ElMessage.error('连接测试失败，请检查网络连接和服务配置');
  } finally {
    testLoading.value = false;
  }
};

// 工具函数
const formatNumber = (num?: number): string => {
  if (!num || num === 0) return '0';
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

const formatDate = (dateStr: string): string => {
  if (!dateStr) return '-';
  try {
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN');
  } catch {
    return dateStr;
  }
};

const getModelDisplayName = (model?: string): string => {
  if (!model) return '-';
  const parts = model.split('@');
  return parts[0] || model;
};

const getChunkMethodName = (method?: string): string => {
  if (!method) return '-';
  const methodMap: Record<string, string> = {
    naive: '通用',
    book: '书籍',
    email: '邮件',
    laws: '法律',
    manual: '手动',
    one: '单一',
    paper: '论文',
    picture: '图片',
    presentation: '演示文稿',
    qa: '问答',
    table: '表格',
    tag: '标签'
  };
  return methodMap[method] || method;
};
</script>

<style scoped>
.knowledge-base-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-title .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stats-icon {
  margin-right: 15px;
  font-size: 32px;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-left {
  display: flex;
  align-items: center;
}

.search-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.kb-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.kb-name-cell {
  display: flex;
  align-items: center;
}

.kb-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.kb-info {
  flex: 1;
  min-width: 0;
}

.kb-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.kb-description {
  font-size: 12px;
  color: #909399;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.stats-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.stat-label {
  color: #909399;
  margin-right: 4px;
  min-width: 32px;
}

.stat-value {
  color: #303133;
  font-weight: 500;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.kb-detail {
  padding: 10px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-base-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .header-right {
    justify-content: flex-start;
  }

  .search-bar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .search-left,
  .search-right {
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9fa;
}

/* 对话框样式优化 */
:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #f0f0f0;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

/* 按钮样式优化 */
.el-button {
  border-radius: 6px;
  font-weight: 500;
}

/* 卡片样式优化 */
:deep(.el-card__body) {
  padding: 20px;
}

/* 标签样式优化 */
.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 描述列表样式优化 */
:deep(.el-descriptions__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #303133;
}
</style>