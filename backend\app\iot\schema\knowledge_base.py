#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库相关的 Schema 定义

基于 RAGFlow API 规范设计的知识库数据模型
"""
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ParserConfig(BaseModel):
    """解析器配置"""
    chunk_token_num: int = Field(default=512, ge=1, le=2048, description="分块token数量")
    delimiter: str = Field(default="\\n", description="分隔符")
    html4excel: bool = Field(default=False, description="是否将Excel转换为HTML格式")
    layout_recognize: str = Field(default="DeepDOC", description="布局识别方法")
    auto_keywords: int = Field(default=0, ge=0, le=32, description="自动关键词数量")
    auto_questions: int = Field(default=0, ge=0, le=10, description="自动问题数量")
    task_page_size: int = Field(default=12, ge=1, description="PDF任务页面大小")
    tag_kb_ids: Optional[List[str]] = Field(default=None, description="标签知识库ID列表")
    raptor: Dict[str, Any] = Field(default_factory=lambda: {"use_raptor": False}, description="RAPTOR设置")
    graphrag: Dict[str, Any] = Field(default_factory=lambda: {"use_graphrag": False}, description="GRAPHRAG设置")


class KnowledgeBaseCreate(BaseModel):
    """创建知识库请求"""
    name: str = Field(..., max_length=128, description="知识库名称")
    avatar: Optional[str] = Field(None, max_length=65535, description="头像Base64编码")
    description: Optional[str] = Field(None, max_length=65535, description="知识库描述")
    embedding_model: str = Field(
        default="BAAI/bge-large-zh-v1.5@BAAI",
        max_length=255,
        description="嵌入模型名称"
    )
    permission: str = Field(default="me", description="访问权限 (me/team)")
    chunk_method: str = Field(
        default="naive",
        description="分块方法 (naive/book/email/laws/manual/one/paper/picture/presentation/qa/table/tag)"
    )
    pagerank: int = Field(default=0, ge=0, le=100, description="页面排名")
    parser_config: Optional[ParserConfig] = Field(default_factory=ParserConfig, description="解析器配置")


class KnowledgeBaseUpdate(BaseModel):
    """更新知识库请求"""
    name: Optional[str] = Field(None, max_length=128, description="知识库名称")
    avatar: Optional[str] = Field(None, max_length=65535, description="头像Base64编码")
    description: Optional[str] = Field(None, max_length=65535, description="知识库描述")
    embedding_model: Optional[str] = Field(None, max_length=255, description="嵌入模型名称")
    permission: Optional[str] = Field(None, description="访问权限 (me/team)")
    chunk_method: Optional[str] = Field(None, description="分块方法")
    pagerank: Optional[int] = Field(None, ge=0, le=100, description="页面排名")
    parser_config: Optional[ParserConfig] = Field(None, description="解析器配置")


class KnowledgeBaseInfo(BaseModel):
    """知识库信息响应"""
    id: str = Field(..., description="知识库ID")
    name: str = Field(..., description="知识库名称")
    avatar: Optional[str] = Field(None, description="头像")
    description: Optional[str] = Field(None, description="知识库描述")
    embedding_model: str = Field(..., description="嵌入模型名称")
    permission: str = Field(..., description="访问权限")
    chunk_method: str = Field(..., description="分块方法")
    pagerank: int = Field(..., description="页面排名")
    parser_config: Dict[str, Any] = Field(..., description="解析器配置")
    
    # 统计信息
    chunk_count: int = Field(default=0, description="分块数量")
    document_count: int = Field(default=0, description="文档数量")
    token_num: int = Field(default=0, description="token数量")
    
    # 系统信息
    status: str = Field(default="1", description="状态")
    language: str = Field(default="Chinese", description="语言")
    similarity_threshold: float = Field(default=0.2, description="相似度阈值")
    vector_similarity_weight: float = Field(default=0.3, description="向量相似度权重")
    
    # 时间信息
    create_time: int = Field(..., description="创建时间戳")
    update_time: int = Field(..., description="更新时间戳")
    create_date: str = Field(..., description="创建日期")
    update_date: str = Field(..., description="更新日期")
    
    # 用户信息
    created_by: str = Field(..., description="创建者ID")
    tenant_id: str = Field(..., description="租户ID")


class KnowledgeBaseList(BaseModel):
    """知识库列表响应"""
    data: List[KnowledgeBaseInfo] = Field(..., description="知识库列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")


class KnowledgeBaseDelete(BaseModel):
    """删除知识库请求"""
    ids: Optional[List[str]] = Field(None, description="要删除的知识库ID列表，null表示删除所有")


class KnowledgeBaseQuery(BaseModel):
    """知识库查询参数"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=30, ge=1, le=100, description="每页大小")
    orderby: str = Field(default="create_time", description="排序字段 (create_time/update_time)")
    desc: bool = Field(default=True, description="是否降序排列")
    name: Optional[str] = Field(None, description="知识库名称过滤")
    id: Optional[str] = Field(None, description="知识库ID过滤")


class KnowledgeBaseStats(BaseModel):
    """知识库统计信息"""
    total_kb: int = Field(..., description="知识库总数")
    total_documents: int = Field(..., description="文档总数")
    total_chunks: int = Field(..., description="分块总数")
    total_tokens: int = Field(..., description="token总数")
    active_kb: int = Field(..., description="活跃知识库数")
    recent_created: int = Field(..., description="最近创建的知识库数")
    storage_used: str = Field(..., description="存储使用量")
    last_update: datetime = Field(..., description="最后更新时间")


class KnowledgeBaseResponse(BaseModel):
    """知识库API响应基类"""
    code: int = Field(..., description="响应码")
    message: Optional[str] = Field(None, description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")


class KnowledgeBaseCreateResponse(KnowledgeBaseResponse):
    """创建知识库响应"""
    data: Optional[KnowledgeBaseInfo] = Field(None, description="创建的知识库信息")


class KnowledgeBaseListResponse(KnowledgeBaseResponse):
    """知识库列表响应"""
    data: Optional[List[KnowledgeBaseInfo]] = Field(None, description="知识库列表")


class KnowledgeBaseDetailResponse(KnowledgeBaseResponse):
    """知识库详情响应"""
    data: Optional[KnowledgeBaseInfo] = Field(None, description="知识库详情")


class KnowledgeBaseStatsResponse(KnowledgeBaseResponse):
    """知识库统计响应"""
    data: Optional[KnowledgeBaseStats] = Field(None, description="统计信息")


# 错误响应模型
class ErrorResponse(BaseModel):
    """错误响应"""
    code: int = Field(..., description="错误码")
    message: str = Field(..., description="错误消息")


# 常用的错误码定义
class KnowledgeBaseErrorCodes:
    """知识库错误码"""
    SUCCESS = 0
    INVALID_PARAMETER = 100
    KB_NAME_EXISTS = 101
    KB_NOT_FOUND = 102
    PERMISSION_DENIED = 103
    INVALID_EMBEDDING_MODEL = 104
    CHUNK_COUNT_NOT_ZERO = 105
    TENANT_ID_CHANGE_DENIED = 106
    QUERY_ERROR = 400
    INTERNAL_ERROR = 500
