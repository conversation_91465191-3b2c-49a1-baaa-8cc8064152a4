#!/usr/bin/env python3
import asyncio
import httpx
import socket
import json
import time

async def test_tcp_connection():
    """测试TCP连接"""
    print("🔍 测试TCP连接...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        result = sock.connect_ex(('*************', 6610))
        sock.close()

        if result == 0:
            print('✅ TCP连接成功')
            return True
        else:
            print(f'❌ TCP连接失败，错误码: {result}')
            return False
    except Exception as e:
        print(f'❌ TCP连接异常: {e}')
        return False

async def test_http_basic():
    """测试基础HTTP连接"""
    print("\n🌐 测试HTTP基础连接...")

    # 尝试多个路径
    test_paths = [
        '/',
        '/api/v1/datasets',
        '/health',
        '/api/health'
    ]

    for path in test_paths:
        try:
            print(f"  尝试路径: {path}")
            # 配置httpx客户端，模拟curl的行为
            async with httpx.AsyncClient(
                timeout=httpx.Timeout(30.0, connect=10.0),
                verify=False,  # 禁用SSL验证
                headers={'User-Agent': 'curl/7.68.0'},  # 使用curl的用户代理
                follow_redirects=True
            ) as client:
                url = f'http://*************:6610{path}'

                if path == '/api/v1/datasets':
                    # 对于API端点，添加认证头
                    headers = {
                        'Authorization': 'Bearer ragflow-c2ZmY3MzVhMDkwYzExZjA5MzIwZjZjZW',
                        'User-Agent': 'curl/7.68.0'
                    }
                    response = await client.get(url, headers=headers, params={'page': 1, 'page_size': 1})
                else:
                    response = await client.get(url)

                print(f'    状态码: {response.status_code}')

                if response.status_code in [200, 404, 403, 401]:
                    print(f'    ✅ HTTP服务正在运行 (路径: {path})')
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            print(f'    响应数据: {json.dumps(data, indent=4, ensure_ascii=False)[:200]}...')
                        except:
                            print(f'    响应文本: {response.text[:100]}...')
                    return True
                else:
                    print(f'    ⚠️ 状态码: {response.status_code}')

        except httpx.ConnectError:
            print(f'    ❌ 连接被拒绝')
        except httpx.TimeoutException:
            print(f'    ❌ 连接超时')
        except Exception as e:
            print(f'    ❌ 异常: {e}')

    print('❌ 所有HTTP路径测试失败')
    return False

async def test_ragflow_api():
    """测试RAGFlow API"""
    print("\n📡 测试RAGFlow API...")
    try:
        async with httpx.AsyncClient(
            timeout=httpx.Timeout(30.0, connect=10.0),
            verify=False,
            headers={'User-Agent': 'curl/7.68.0'},
            follow_redirects=True
        ) as client:
            response = await client.get(
                'http://*************:6610/api/v1/datasets',
                headers={
                    'Authorization': 'Bearer ragflow-c2ZmY3MzVhMDkwYzExZjA5MzIwZjZjZW',
                    'User-Agent': 'curl/7.68.0'
                },
                params={'page': 1, 'page_size': 1}
            )
            print(f'API状态码: {response.status_code}')
            print(f'响应头: {dict(response.headers)}')

            if response.status_code == 200:
                data = response.json()
                print(f'响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}')
                if data.get("code") == 0:
                    datasets = data.get("data", [])
                    print(f'✅ RAGFlow API正常，找到 {len(datasets)} 个数据集')
                    return True
                else:
                    print(f'⚠️ API返回错误: {data.get("message", "未知错误")}')
                    return False
            else:
                print(f'❌ API响应异常: {response.status_code}')
                print(f'响应内容: {response.text[:500]}...')
                return False
    except Exception as e:
        print(f'❌ RAGFlow API测试失败: {e}')
        return False

async def test_create_dataset():
    """测试创建数据集"""
    print("\n🔨 测试创建数据集...")

    # 生成唯一的数据集名称
    timestamp = int(time.time())
    dataset_name = f"FastAPI_Test_{timestamp}"

    # 尝试不同的嵌入模型配置
    create_data = {
        "name": dataset_name,
        "description": "FastAPI集成测试数据集",
        # 不指定embedding_model，让系统使用默认值
        "permission": "me",
        "chunk_method": "naive"
        # 不指定parser_config，让系统使用默认值
    }

    try:
        async with httpx.AsyncClient(
            timeout=httpx.Timeout(30.0, connect=10.0),
            verify=False,
            headers={'User-Agent': 'curl/7.68.0'},
            follow_redirects=True
        ) as client:
            response = await client.post(
                'http://*************:6610/api/v1/datasets',
                headers={
                    'Authorization': 'Bearer ragflow-c2ZmY3MzVhMDkwYzExZjA5MzIwZjZjZW',
                    'Content-Type': 'application/json',
                    'User-Agent': 'curl/7.68.0'
                },
                json=create_data
            )

            print(f'创建API状态码: {response.status_code}')

            if response.status_code == 200:
                data = response.json()
                print(f'创建响应: {json.dumps(data, indent=2, ensure_ascii=False)}')

                if data.get("code") == 0:
                    dataset_info = data.get("data", {})
                    dataset_id = dataset_info.get("id")
                    print(f'✅ 数据集创建成功!')
                    print(f'ID: {dataset_id}')
                    print(f'名称: {dataset_info.get("name")}')
                    print(f'嵌入模型: {dataset_info.get("embedding_model")}')
                    return dataset_id
                else:
                    print(f'⚠️ 创建失败: {data.get("message", "未知错误")}')
                    return None
            else:
                print(f'❌ 创建API响应异常: {response.status_code}')
                print(f'响应内容: {response.text}')
                return None

    except Exception as e:
        print(f'❌ 创建数据集失败: {e}')
        return None

async def main():
    print("=" * 60)
    print("RAGFlow 连接诊断测试")
    print("=" * 60)

    # 测试1: TCP连接
    tcp_ok = await test_tcp_connection()

    # 测试2: HTTP基础连接
    http_ok = False
    if tcp_ok:
        http_ok = await test_http_basic()

    # 测试3: RAGFlow API
    api_ok = False
    if http_ok:
        api_ok = await test_ragflow_api()

    # 测试4: 创建数据集
    create_ok = False
    dataset_id = None
    if api_ok:
        dataset_id = await test_create_dataset()
        create_ok = dataset_id is not None

    print("\n" + "=" * 60)
    print("诊断结果汇总:")
    print(f"TCP连接: {'✅ 正常' if tcp_ok else '❌ 失败'}")
    print(f"HTTP服务: {'✅ 正常' if http_ok else '❌ 失败'}")
    print(f"RAGFlow API: {'✅ 正常' if api_ok else '❌ 失败'}")
    print(f"创建数据集: {'✅ 正常' if create_ok else '❌ 失败'}")

    if not tcp_ok:
        print("\n🔧 建议检查:")
        print("1. RAGFlow服务是否已启动")
        print("2. 服务器IP地址 ************* 是否可达")
        print("3. 端口 6610 是否开放")
        print("4. 防火墙设置")
    elif not http_ok:
        print("\n🔧 建议检查:")
        print("1. RAGFlow HTTP服务配置")
        print("2. 服务启动状态")
    elif not api_ok:
        print("\n🔧 建议检查:")
        print("1. API Key是否正确")
        print("2. RAGFlow API版本兼容性")
    elif not create_ok:
        print("\n🔧 建议检查:")
        print("1. 数据集创建权限")
        print("2. 嵌入模型配置")
        print("3. 请求参数格式")
    else:
        print(f"\n🎉 所有测试通过！RAGFlow服务完全正常")
        if dataset_id:
            print(f"✅ 成功创建测试数据集: {dataset_id}")

    print("=" * 60)
    return api_ok and create_ok

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
