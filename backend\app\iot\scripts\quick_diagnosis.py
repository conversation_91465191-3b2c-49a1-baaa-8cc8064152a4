#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速诊断脚本 - 检查知识库管理功能问题
"""
import asyncio
import sys
import os
import json
import httpx
import traceback

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

async def test_local_api():
    """测试本地 API 接口"""
    print("🔍 测试本地 API 接口...")
    
    base_url = "http://localhost:8000"
    
    # 测试健康检查
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            print(f"  测试: {base_url}/api/v1/iot/kb/datasets/health")
            response = await client.get(f"{base_url}/api/v1/iot/kb/datasets/health")
            print(f"  状态码: {response.status_code}")
            print(f"  响应: {response.text}")
            
            if response.status_code == 200:
                print("  ✅ 健康检查通过")
            else:
                print("  ❌ 健康检查失败")
                
    except Exception as e:
        print(f"  ❌ 健康检查异常: {e}")
    
    # 测试知识库列表接口（无认证）
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            print(f"\n  测试: {base_url}/api/v1/iot/kb/datasets")
            response = await client.get(f"{base_url}/api/v1/iot/kb/datasets")
            print(f"  状态码: {response.status_code}")
            print(f"  响应: {response.text[:500]}...")
            
            if response.status_code == 401:
                print("  ✅ 认证检查正常（需要 Token）")
            elif response.status_code == 200:
                print("  ⚠️ 接口可访问但可能缺少认证")
            else:
                print("  ❌ 接口异常")
                
    except Exception as e:
        print(f"  ❌ 接口测试异常: {e}")

async def test_external_kb_service():
    """测试外部知识库服务"""
    print("\n🌐 测试外部知识库服务...")
    
    kb_url = "http://192.168.66.40:6610"
    api_key = "ragflow-c2ZmY3MzVhMDkwYzExZjA5MzIwZjZjZW"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试连接
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            print(f"  测试连接: {kb_url}")
            response = await client.get(f"{kb_url}/api/v1/health", headers=headers)
            print(f"  状态码: {response.status_code}")
            print(f"  响应: {response.text}")
            
            if response.status_code == 200:
                print("  ✅ 外部服务连接成功")
            else:
                print("  ❌ 外部服务连接失败")
                
    except Exception as e:
        print(f"  ❌ 外部服务连接异常: {e}")
    
    # 测试数据集接口
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            print(f"\n  测试数据集接口: {kb_url}/api/v1/datasets")
            response = await client.get(f"{kb_url}/api/v1/datasets", headers=headers)
            print(f"  状态码: {response.status_code}")
            print(f"  响应: {response.text[:500]}...")
            
            if response.status_code == 200:
                print("  ✅ 数据集接口正常")
            else:
                print("  ❌ 数据集接口异常")
                
    except Exception as e:
        print(f"  ❌ 数据集接口异常: {e}")

def test_imports():
    """测试模块导入"""
    print("\n📦 测试模块导入...")
    
    modules_to_test = [
        "backend.app.iot.api.v1.knowledge_base",
        "backend.app.iot.service.knowledge_base_service",
        "backend.app.iot.schema.knowledge_base",
        "backend.common.security.iot_permission",
        "backend.common.security.iot_adapter",
    ]
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"  ✅ {module_name}")
        except Exception as e:
            print(f"  ❌ {module_name}: {e}")

def test_configuration():
    """测试配置"""
    print("\n⚙️ 测试配置...")
    
    try:
        from backend.core.conf import settings
        
        config_items = [
            ("IOT_INTEGRATION_ENABLED", settings.IOT_INTEGRATION_ENABLED),
            ("IOT_JWT_SECRET_KEY", bool(settings.IOT_JWT_SECRET_KEY)),
            ("KNOWLEDGE_BASE_URL", settings.KNOWLEDGE_BASE_URL),
            ("KNOWLEDGE_BASE_API_KEY", bool(settings.KNOWLEDGE_BASE_API_KEY)),
        ]
        
        for name, value in config_items:
            status = "✅" if value else "❌"
            print(f"  {status} {name}: {value}")
            
    except Exception as e:
        print(f"  ❌ 配置加载异常: {e}")

async def test_service_methods():
    """测试服务方法"""
    print("\n🔧 测试服务方法...")
    
    try:
        from backend.app.iot.service.knowledge_base_service import KnowledgeBaseService
        
        service = KnowledgeBaseService()
        print(f"  ✅ 服务实例创建成功")
        print(f"  知识库服务地址: {service.kb_base_url}")
        print(f"  API Key 配置: {'已配置' if service.kb_api_key else '未配置'}")
        print(f"  超时设置: {service.timeout}秒")
        
        # 测试数据转换方法
        test_data = {
            "id": "test_id",
            "name": "测试知识库",
            "description": "测试描述",
            "embedding_model": "test_model",
            "permission": "me",
            "chunk_method": "naive",
            "pagerank": 0,
            "chunk_count": 0,
            "document_count": 0,
            "token_num": 0,
            "status": "1"
        }
        
        kb_info = service._convert_to_kb_info(test_data, "test_user")
        print(f"  ✅ 数据转换方法正常")
        
    except Exception as e:
        print(f"  ❌ 服务测试异常: {e}")
        traceback.print_exc()

def check_route_registration():
    """检查路由注册"""
    print("\n🛣️ 检查路由注册...")
    
    try:
        from backend.app.iot.api.v1.router import iot_v1_router
        
        print(f"  IoT 路由数量: {len(iot_v1_router.routes)}")
        
        for route in iot_v1_router.routes:
            if hasattr(route, 'path'):
                print(f"  - {route.path}")
        
        print("  ✅ 路由注册检查完成")
        
    except Exception as e:
        print(f"  ❌ 路由检查异常: {e}")

async def main():
    """主函数"""
    print("🚀 开始快速诊断...\n")
    
    # 运行所有测试
    test_imports()
    test_configuration()
    check_route_registration()
    await test_service_methods()
    await test_local_api()
    await test_external_kb_service()
    
    print("\n" + "="*60)
    print("📋 诊断建议:")
    print("="*60)
    print("1. 确保后端服务正在运行 (python -m uvicorn main:app --reload)")
    print("2. 检查 .env 文件中的配置是否正确")
    print("3. 确保外部知识库服务 (192.168.66.40:6610) 可访问")
    print("4. 检查 API Key 是否有效")
    print("5. 确保 IoT 路由已在主应用中注册")
    print("6. 检查前端 API 调用地址是否正确")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
