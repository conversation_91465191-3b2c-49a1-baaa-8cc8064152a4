#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IoT 设备管理 API

提供 IoT 设备的增删改查和控制接口
"""
from typing import List

from fastapi import APIRouter, Depends, Request

from backend.common.response.response_schema import ResponseModel, response_base
from backend.common.security.iot_permission import (
    DependsIoTRBAC,
    IoTPermissions,
    require_iot_permission
)
from backend.common.security.jwt import DependsJwtAuth

router = APIRouter()


@router.get(
    '/list',
    summary='获取设备列表',
    dependencies=[
        Depends(require_iot_permission(IoTPermissions.DEVICE_LIST)),
        DependsJwtAuth,
        DependsIoTRBAC
    ]
)
async def get_device_list(request: Request) -> ResponseModel:
    """
    获取 IoT 设备列表
    
    需要 'iot:device:list' 权限
    """
    # 模拟设备数据
    devices = [
        {
            'id': 1,
            'name': '温度传感器-001',
            'type': 'temperature_sensor',
            'status': 'online',
            'location': '机房A-01',
            'last_data_time': '2024-08-04 11:25:00',
            'value': 23.5,
            'unit': '°C'
        },
        {
            'id': 2,
            'name': '湿度传感器-002',
            'type': 'humidity_sensor',
            'status': 'online',
            'location': '机房A-02',
            'last_data_time': '2024-08-04 11:24:30',
            'value': 65.2,
            'unit': '%'
        },
        {
            'id': 3,
            'name': '智能开关-003',
            'type': 'smart_switch',
            'status': 'offline',
            'location': '办公室B-01',
            'last_data_time': '2024-08-04 10:15:00',
            'value': 0,
            'unit': 'bool'
        }
    ]
    
    return await response_base.success(
        data={
            'devices': devices,
            'total': len(devices),
            'user_id': request.user.id
        }
    )


@router.get(
    '/{device_id}',
    summary='获取设备详情',
    dependencies=[
        Depends(require_iot_permission(IoTPermissions.DEVICE_VIEW)),
        DependsJwtAuth,
        DependsIoTRBAC
    ]
)
async def get_device_detail(device_id: int, request: Request) -> ResponseModel:
    """
    获取指定设备的详细信息
    
    需要 'iot:device:view' 权限
    """
    # 模拟设备详情数据
    device_detail = {
        'id': device_id,
        'name': f'设备-{device_id:03d}',
        'type': 'temperature_sensor',
        'status': 'online',
        'location': f'位置-{device_id}',
        'description': f'这是设备 {device_id} 的详细描述',
        'created_time': '2024-01-01 00:00:00',
        'last_data_time': '2024-08-04 11:25:00',
        'properties': {
            'model': 'TS-001',
            'manufacturer': 'IoT Company',
            'firmware_version': '1.2.3',
            'ip_address': f'192.168.1.{100 + device_id}',
            'mac_address': f'00:11:22:33:44:{device_id:02x}'
        },
        'current_data': {
            'temperature': 23.5,
            'humidity': 65.2,
            'timestamp': '2024-08-04 11:25:00'
        }
    }
    
    return await response_base.success(data=device_detail)


@router.post(
    '/control/{device_id}',
    summary='控制设备',
    dependencies=[
        Depends(require_iot_permission(IoTPermissions.DEVICE_CONTROL)),
        DependsJwtAuth,
        DependsIoTRBAC
    ]
)
async def control_device(device_id: int, command: dict, request: Request) -> ResponseModel:
    """
    控制指定设备
    
    需要 'iot:device:control' 权限
    """
    # 模拟设备控制逻辑
    control_result = {
        'device_id': device_id,
        'command': command,
        'status': 'success',
        'message': f'设备 {device_id} 控制命令执行成功',
        'executed_by': request.user.username,
        'executed_time': '2024-08-04 11:30:00'
    }
    
    return await response_base.success(
        data=control_result,
        msg=f'设备 {device_id} 控制成功'
    )


@router.get(
    '/data/{device_id}',
    summary='获取设备数据',
    dependencies=[
        Depends(require_iot_permission(IoTPermissions.DATA_VIEW)),
        DependsJwtAuth,
        DependsIoTRBAC
    ]
)
async def get_device_data(device_id: int, request: Request, limit: int = 100) -> ResponseModel:
    """
    获取指定设备的历史数据
    
    需要 'iot:data:view' 权限
    """
    # 模拟设备历史数据
    data_points = []
    for i in range(min(limit, 10)):  # 限制返回数据量
        data_points.append({
            'timestamp': f'2024-08-04 11:{25-i:02d}:00',
            'temperature': 23.5 + (i * 0.1),
            'humidity': 65.2 - (i * 0.2),
            'status': 'normal'
        })
    
    return await response_base.success(
        data={
            'device_id': device_id,
            'data_points': data_points,
            'total_count': len(data_points),
            'requested_by': request.user.username
        }
    )


@router.get(
    '/statistics/summary',
    summary='获取设备统计摘要',
    dependencies=[
        Depends(require_iot_permission([IoTPermissions.DEVICE_LIST, IoTPermissions.DATA_VIEW], require_all=False)),
        DependsJwtAuth,
        DependsIoTRBAC
    ]
)
async def get_device_statistics(request: Request) -> ResponseModel:
    """
    获取设备统计摘要信息
    
    需要 'iot:device:list' 或 'iot:data:view' 权限之一
    """
    # 模拟统计数据
    statistics = {
        'total_devices': 156,
        'online_devices': 142,
        'offline_devices': 14,
        'device_types': {
            'temperature_sensor': 45,
            'humidity_sensor': 38,
            'smart_switch': 32,
            'camera': 25,
            'other': 16
        },
        'data_points_today': 15420,
        'alerts_count': 3,
        'last_update': '2024-08-04 11:30:00'
    }
    
    return await response_base.success(data=statistics)
