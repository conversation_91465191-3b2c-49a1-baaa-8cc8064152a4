#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IoT 系统集成配置示例

此文件展示了如何配置 IoT 系统集成所需的参数
"""

# ==================== IoT 集成配置示例 ====================

# 1. 环境变量配置（推荐方式）
# 在 .env 文件中添加以下配置：

ENV_CONFIG_EXAMPLE = """
# IoT 系统集成配置
IOT_INTEGRATION_ENABLED=true
IOT_JWT_SECRET_KEY=your-shared-secret-key-with-java-system
IOT_JWT_ALGORITHM=HS256
IOT_JWT_ISSUER=TS-IOT-SYS
IOT_TOKEN_PREFIX=login_tokens:
IOT_USER_PREFIX=user_info:
IOT_PERMISSION_PREFIX=user_permissions:
IOT_USER_CACHE_TTL=300
IOT_PERMISSION_CACHE_TTL=600
IOT_DEBUG_MODE=false
"""

# 2. 代码配置示例
CODE_CONFIG_EXAMPLE = {
    'IOT_INTEGRATION_ENABLED': True,
    'IOT_JWT_SECRET_KEY': 'your-shared-secret-key-with-java-system',
    'IOT_JWT_ALGORITHM': 'HS256',
    'IOT_JWT_ISSUER': 'TS-IOT-SYS',
    'IOT_TOKEN_PREFIX': 'login_tokens:',
    'IOT_USER_PREFIX': 'user_info:',
    'IOT_PERMISSION_PREFIX': 'user_permissions:',
    'IOT_USER_CACHE_TTL': 300,
    'IOT_PERMISSION_CACHE_TTL': 600,
    'IOT_DEBUG_MODE': False
}

# ==================== 使用说明 ====================

USAGE_INSTRUCTIONS = """
IoT 系统集成使用说明：

1. 配置步骤：
   - 在 .env 文件中添加 IoT 配置参数
   - 确保 IOT_JWT_SECRET_KEY 与 Java 系统保持一致
   - 设置 IOT_INTEGRATION_ENABLED=true 启用集成

2. 验证配置：
   - 访问 /api/v1/iot/auth/health 检查健康状态
   - 使用 IoT 系统的 JWT token 访问 /api/v1/iot/auth/test

3. API 使用：
   - 所有 IoT API 都在 /api/v1/iot 路径下
   - 使用 Authorization: Bearer <iot-jwt-token> 进行认证
   - 权限控制基于 IoT 权限标识（如 'iot:device:list'）

4. 权限配置：
   - 在系统菜单中添加 IoT 权限标识
   - 为用户角色分配相应的 IoT 菜单权限
   - 支持通配符权限（如 'iot:device:*'）

5. 调试模式：
   - 设置 IOT_DEBUG_MODE=true 启用详细日志
   - 查看认证和权限验证的详细信息
"""

# ==================== 权限配置示例 ====================

IOT_PERMISSION_EXAMPLES = {
    '设备管理权限': [
        'iot:device:list',      # 设备列表查看
        'iot:device:view',      # 设备详情查看
        'iot:device:add',       # 设备添加
        'iot:device:edit',      # 设备编辑
        'iot:device:delete',    # 设备删除
        'iot:device:control',   # 设备控制
    ],
    '数据管理权限': [
        'iot:data:view',        # 数据查看
        'iot:data:export',      # 数据导出
        'iot:data:analysis',    # 数据分析
    ],
    '用户管理权限': [
        'iot:user:list',        # 用户列表
        'iot:user:view',        # 用户详情
        'iot:user:add',         # 用户添加
        'iot:user:edit',        # 用户编辑
        'iot:user:delete',      # 用户删除
    ],
    '系统管理权限': [
        'iot:system:config',    # 系统配置
        'iot:system:monitor',   # 系统监控
        'iot:system:log',       # 系统日志
    ],
    '通配符权限': [
        'iot:device:*',         # 设备所有权限
        'iot:*:view',           # 所有查看权限
        '*:*:*',                # 超级管理员权限
    ]
}

# ==================== 测试用例示例 ====================

TEST_CASES_EXAMPLE = """
# 测试 IoT 认证集成

1. 健康检查测试：
   curl -X GET "http://localhost:8000/api/v1/iot/auth/health"

2. 认证测试（需要有效的 IoT JWT token）：
   curl -X GET "http://localhost:8000/api/v1/iot/auth/test" \\
        -H "Authorization: Bearer <your-iot-jwt-token>"

3. 权限测试：
   curl -X GET "http://localhost:8000/api/v1/iot/auth/permissions/test" \\
        -H "Authorization: Bearer <your-iot-jwt-token>"

4. 设备列表测试：
   curl -X GET "http://localhost:8000/api/v1/iot/device/list" \\
        -H "Authorization: Bearer <your-iot-jwt-token>"

5. 用户信息测试：
   curl -X GET "http://localhost:8000/api/v1/iot/auth/user/info" \\
        -H "Authorization: Bearer <your-iot-jwt-token>"
"""

# ==================== 故障排除指南 ====================

TROUBLESHOOTING_GUIDE = """
IoT 集成故障排除指南：

1. 认证失败：
   - 检查 IOT_JWT_SECRET_KEY 是否与 Java 系统一致
   - 确认 JWT token 格式正确
   - 验证 token 是否在 Redis 中存在

2. 权限验证失败：
   - 检查用户是否在系统中存在
   - 确认用户角色是否分配了相应的菜单权限
   - 验证菜单权限标识是否正确

3. 配置问题：
   - 确认 IOT_INTEGRATION_ENABLED=true
   - 检查所有必需的配置参数是否设置
   - 验证 Redis 连接是否正常

4. API 访问问题：
   - 确认路由是否正确注册
   - 检查中间件是否正常工作
   - 验证依赖注入是否配置正确

5. 调试建议：
   - 启用 IOT_DEBUG_MODE=true
   - 查看应用日志获取详细错误信息
   - 使用健康检查接口验证配置状态
"""

if __name__ == '__main__':
    print("IoT 系统集成配置示例")
    print("=" * 50)
    print(USAGE_INSTRUCTIONS)
    print("\n权限配置示例：")
    for category, permissions in IOT_PERMISSION_EXAMPLES.items():
        print(f"\n{category}:")
        for perm in permissions:
            print(f"  - {perm}")
    print("\n" + "=" * 50)
    print("详细配置请参考 backend/core/conf.py 中的 IoT 配置部分")
