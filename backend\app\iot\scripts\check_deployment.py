#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IoT 知识库管理部署检查脚本

检查系统配置、外部服务连接、权限设置等
"""
import asyncio
import sys
import os
from typing import Dict, List, Tuple
import httpx
import redis

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

from backend.core.conf import settings
from backend.app.iot.service.knowledge_base_service import KnowledgeBaseService


class DeploymentChecker:
    """部署检查器"""
    
    def __init__(self):
        self.results: List[Tuple[str, bool, str]] = []
        self.kb_service = KnowledgeBaseService()
    
    def add_result(self, check_name: str, success: bool, message: str):
        """添加检查结果"""
        self.results.append((check_name, success, message))
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {check_name}: {message}")
    
    def check_environment_variables(self) -> bool:
        """检查环境变量配置"""
        print("\n🔧 检查环境变量配置...")
        
        required_vars = [
            ('IOT_INTEGRATION_ENABLED', settings.IOT_INTEGRATION_ENABLED),
            ('IOT_JWT_SECRET_KEY', settings.IOT_JWT_SECRET_KEY),
            ('KNOWLEDGE_BASE_URL', settings.KNOWLEDGE_BASE_URL),
            ('KNOWLEDGE_BASE_API_KEY', settings.KNOWLEDGE_BASE_API_KEY),
        ]
        
        all_ok = True
        for var_name, var_value in required_vars:
            if not var_value:
                self.add_result(f"环境变量 {var_name}", False, "未配置或为空")
                all_ok = False
            else:
                self.add_result(f"环境变量 {var_name}", True, "已配置")
        
        return all_ok
    
    def check_redis_connection(self) -> bool:
        """检查 Redis 连接"""
        print("\n🔗 检查 Redis 连接...")
        
        try:
            # 创建 Redis 连接
            r = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                password=settings.REDIS_PASSWORD,
                db=settings.REDIS_DATABASE,
                decode_responses=True
            )
            
            # 测试连接
            r.ping()
            self.add_result("Redis 连接", True, f"连接成功 ({settings.REDIS_HOST}:{settings.REDIS_PORT})")
            
            # 测试读写
            test_key = "iot:deployment:test"
            r.set(test_key, "test_value", ex=10)
            value = r.get(test_key)
            r.delete(test_key)
            
            if value == "test_value":
                self.add_result("Redis 读写", True, "读写测试成功")
                return True
            else:
                self.add_result("Redis 读写", False, "读写测试失败")
                return False
                
        except Exception as e:
            self.add_result("Redis 连接", False, f"连接失败: {str(e)}")
            return False
    
    async def check_external_kb_service(self) -> bool:
        """检查外部知识库服务连接"""
        print("\n🌐 检查外部知识库服务...")
        
        try:
            # 测试基本连接
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{settings.KNOWLEDGE_BASE_URL}/api/v1/health")
                
                if response.status_code == 200:
                    self.add_result("知识库服务连接", True, f"连接成功 ({settings.KNOWLEDGE_BASE_URL})")
                else:
                    self.add_result("知识库服务连接", False, f"HTTP {response.status_code}")
                    return False
                    
        except httpx.TimeoutException:
            self.add_result("知识库服务连接", False, "连接超时")
            return False
        except Exception as e:
            self.add_result("知识库服务连接", False, f"连接失败: {str(e)}")
            return False
        
        # 测试 API 认证
        try:
            response = await self.kb_service._make_kb_request("GET", "/api/v1/datasets", params={"page": 1, "page_size": 1})
            
            if response.get("code") == 0:
                self.add_result("知识库服务认证", True, "API Key 有效")
                return True
            else:
                self.add_result("知识库服务认证", False, f"API 响应错误: {response.get('message', 'Unknown error')}")
                return False
                
        except Exception as e:
            if "认证失败" in str(e):
                self.add_result("知识库服务认证", False, "API Key 无效")
            else:
                self.add_result("知识库服务认证", False, f"认证测试失败: {str(e)}")
            return False
    
    def check_iot_permissions(self) -> bool:
        """检查 IoT 权限配置"""
        print("\n🔐 检查 IoT 权限配置...")
        
        from backend.common.security.iot_permission import IoTPermissions
        
        required_permissions = [
            IoTPermissions.KB_CREATE,
            IoTPermissions.KB_LIST,
            IoTPermissions.KB_VIEW,
            IoTPermissions.KB_EDIT,
            IoTPermissions.KB_DELETE,
            IoTPermissions.KB_STATS,
        ]
        
        all_ok = True
        for permission in required_permissions:
            if permission:
                self.add_result(f"权限标识 {permission}", True, "已定义")
            else:
                self.add_result(f"权限标识 {permission}", False, "未定义")
                all_ok = False
        
        return all_ok
    
    def check_api_routes(self) -> bool:
        """检查 API 路由注册"""
        print("\n🛣️ 检查 API 路由...")
        
        try:
            from backend.app.iot.api.v1.router import iot_v1_router
            
            # 检查路由是否包含知识库相关路由
            routes = [route.path for route in iot_v1_router.routes]
            
            expected_routes = [
                "/datasets",
                "/datasets/{dataset_id}",
                "/datasets/stats/summary",
                "/datasets/health"
            ]
            
            all_ok = True
            for route in expected_routes:
                if any(route in r for r in routes):
                    self.add_result(f"路由 {route}", True, "已注册")
                else:
                    self.add_result(f"路由 {route}", False, "未注册")
                    all_ok = False
            
            return all_ok
            
        except Exception as e:
            self.add_result("API 路由检查", False, f"检查失败: {str(e)}")
            return False
    
    def check_dependencies(self) -> bool:
        """检查依赖包"""
        print("\n📦 检查依赖包...")
        
        required_packages = [
            'httpx',
            'redis',
            'fastapi',
            'pydantic',
        ]
        
        all_ok = True
        for package in required_packages:
            try:
                __import__(package)
                self.add_result(f"依赖包 {package}", True, "已安装")
            except ImportError:
                self.add_result(f"依赖包 {package}", False, "未安装")
                all_ok = False
        
        return all_ok
    
    async def run_all_checks(self) -> bool:
        """运行所有检查"""
        print("🚀 开始 IoT 知识库管理部署检查...\n")
        
        checks = [
            ("环境变量", self.check_environment_variables),
            ("依赖包", self.check_dependencies),
            ("Redis 连接", self.check_redis_connection),
            ("API 路由", self.check_api_routes),
            ("IoT 权限", self.check_iot_permissions),
            ("外部知识库服务", self.check_external_kb_service),
        ]
        
        all_passed = True
        
        for check_name, check_func in checks:
            try:
                if asyncio.iscoroutinefunction(check_func):
                    result = await check_func()
                else:
                    result = check_func()
                
                if not result:
                    all_passed = False
                    
            except Exception as e:
                self.add_result(check_name, False, f"检查异常: {str(e)}")
                all_passed = False
        
        return all_passed
    
    def print_summary(self, all_passed: bool):
        """打印检查摘要"""
        print("\n" + "="*60)
        print("📊 部署检查摘要")
        print("="*60)
        
        passed = sum(1 for _, success, _ in self.results if success)
        total = len(self.results)
        
        print(f"总检查项: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {passed/total*100:.1f}%")
        
        if all_passed:
            print("\n🎉 所有检查通过！系统已准备就绪。")
        else:
            print("\n⚠️ 部分检查失败，请修复后重新检查。")
            print("\n失败的检查项:")
            for name, success, message in self.results:
                if not success:
                    print(f"  ❌ {name}: {message}")
        
        print("\n" + "="*60)
    
    def generate_report(self) -> str:
        """生成检查报告"""
        report = []
        report.append("# IoT 知识库管理部署检查报告")
        report.append("")
        report.append(f"检查时间: {asyncio.get_event_loop().time()}")
        report.append("")
        
        for name, success, message in self.results:
            status = "✅" if success else "❌"
            report.append(f"{status} **{name}**: {message}")
        
        return "\n".join(report)


async def main():
    """主函数"""
    checker = DeploymentChecker()
    
    try:
        all_passed = await checker.run_all_checks()
        checker.print_summary(all_passed)
        
        # 生成报告文件
        report = checker.generate_report()
        report_file = "deployment_check_report.md"
        with open(report_file, "w", encoding="utf-8") as f:
            f.write(report)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        # 返回适当的退出码
        sys.exit(0 if all_passed else 1)
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 检查过程中发生异常: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
