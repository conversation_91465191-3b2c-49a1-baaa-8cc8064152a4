import axios from 'axios';
import Cookies from 'js-cookie';

// 创建专门用于知识库服务的 axios 实例
const kbRequest = axios.create({
  baseURL: import.meta.env.VITE_FASTAPI_URL || 'http://localhost:8000', // FastAPI 后端地址
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
});

/**
 * 获取认证token
 * 按优先级从不同存储位置获取token
 */
function getAuthToken(): string {
  // 优先从Cookie获取（与主系统保持一致）
  let token = Cookies.get('token');

  // 备用方式：从sessionStorage或localStorage获取
  if (!token) {
    token = sessionStorage.getItem('token') || localStorage.getItem('token') || '';
  }

  return token || '';
}

// 添加请求拦截器，自动添加认证头
kbRequest.interceptors.request.use(
  (config) => {
    const token = getAuthToken();

    if (token) {
      // 确保token有Bearer前缀
      config.headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 添加响应拦截器
kbRequest.interceptors.response.use(
  (response) => {
    return response.data; // 直接返回 data 部分
  },
  (error) => {
    console.error('知识库 API 请求失败:', error);
    return Promise.reject(error);
  }
);

// 知识库相关接口

/**
 * 知识库数据类型定义
 */
export interface KnowledgeBase {
  id?: string;
  name: string;
  avatar?: string;
  description?: string;
  embedding_model?: string;
  permission?: 'me' | 'team';
  chunk_method?: string;
  pagerank?: number;
  parser_config?: any;
  chunk_count?: number;
  document_count?: number;
  token_num?: number;
  status?: string;
  language?: string;
  similarity_threshold?: number;
  vector_similarity_weight?: number;
  create_time?: number;
  update_time?: number;
  create_date?: string;
  update_date?: string;
  created_by?: string;
  tenant_id?: string;
}

/**
 * 创建知识库请求参数
 */
export interface CreateKnowledgeBaseParams {
  name: string;
  avatar?: string;
  description?: string;
  embedding_model?: string;
  permission?: 'me' | 'team';
  chunk_method?: string;
  pagerank?: number;
  parser_config?: {
    chunk_token_num?: number;
    delimiter?: string;
    html4excel?: boolean;
    layout_recognize?: string;
    auto_keywords?: number;
    auto_questions?: number;
    task_page_size?: number;
    tag_kb_ids?: string[];
    raptor?: any;
    graphrag?: any;
  };
}

/**
 * 更新知识库请求参数
 */
export interface UpdateKnowledgeBaseParams {
  name?: string;
  avatar?: string;
  description?: string;
  embedding_model?: string;
  permission?: 'me' | 'team';
  chunk_method?: string;
  pagerank?: number;
  parser_config?: any;
}

/**
 * 知识库列表查询参数
 */
export interface KnowledgeBaseQueryParams {
  page?: number;
  page_size?: number;
  orderby?: 'create_time' | 'update_time';
  desc?: boolean;
  name?: string;
  id?: string;
}

/**
 * 知识库统计信息
 */
export interface KnowledgeBaseStats {
  total_kb: number;
  total_documents: number;
  total_chunks: number;
  total_tokens: number;
  active_kb: number;
  recent_created: number;
  storage_used: string;
  last_update: string;
}

/**
 * API 响应格式
 */
export interface ApiResponse<T = any> {
  code: number;
  message?: string;
  data?: T;
}

/**
 * 创建知识库
 */
export function createKnowledgeBase(data: CreateKnowledgeBaseParams) {
  return kbRequest.post('/api/v1/iot/kb/datasets', data);
}

/**
 * 获取知识库列表
 */
export function getKnowledgeBaseList(params?: KnowledgeBaseQueryParams) {
  return kbRequest.get('/api/v1/iot/kb/datasets', { params });
}

/**
 * 获取知识库详情
 */
export function getKnowledgeBaseDetail(id: string) {
  return kbRequest.get(`/api/v1/iot/kb/datasets/${id}`);
}

/**
 * 更新知识库
 */
export function updateKnowledgeBase(id: string, data: UpdateKnowledgeBaseParams) {
  return kbRequest.put(`/api/v1/iot/kb/datasets/${id}`, data);
}

/**
 * 删除知识库
 */
export function deleteKnowledgeBases(ids?: string[]) {
  return kbRequest.delete('/api/v1/iot/kb/datasets', { data: { ids } });
}

/**
 * 获取知识库统计信息
 */
export function getKnowledgeBaseStats() {
  return kbRequest.get('/api/v1/iot/kb/datasets/stats/summary');
}

/**
 * 知识库服务健康检查
 */
export function checkKnowledgeBaseHealth() {
  return kbRequest.get('/api/v1/iot/kb/health');
}

/**
 * 批量删除知识库
 */
export function batchDeleteKnowledgeBases(ids: string[]) {
  return deleteKnowledgeBases(ids);
}

/**
 * 删除所有知识库
 */
export function deleteAllKnowledgeBases() {
  return deleteKnowledgeBases(undefined);
}

/**
 * 搜索知识库
 */
export function searchKnowledgeBases(keyword: string, params?: Omit<KnowledgeBaseQueryParams, 'name'>) {
  return getKnowledgeBaseList({
    ...params,
    name: keyword
  });
}

/**
 * 获取知识库分页列表
 */
export function getKnowledgeBasePageList(page: number = 1, pageSize: number = 30, params?: Omit<KnowledgeBaseQueryParams, 'page' | 'page_size'>) {
  return getKnowledgeBaseList({
    page,
    page_size: pageSize,
    ...params
  });
}

/**
 * 复制知识库（基于现有知识库创建新的）
 */
export async function copyKnowledgeBase(sourceId: string, newName: string) {
  try {
    const response = await getKnowledgeBaseDetail(sourceId);
    if (response.data && response.code === 0) {
      const sourceKb = response.data;
      const newKbData: CreateKnowledgeBaseParams = {
        name: newName,
        description: sourceKb.description ? `${sourceKb.description} (副本)` : '知识库副本',
        embedding_model: sourceKb.embedding_model || 'BAAI/bge-large-zh-v1.5@BAAI',
        permission: sourceKb.permission || 'me',
        chunk_method: sourceKb.chunk_method || 'naive',
        pagerank: sourceKb.pagerank || 0,
        parser_config: sourceKb.parser_config
      };
      return createKnowledgeBase(newKbData);
    }
    throw new Error('获取源知识库信息失败');
  } catch (error) {
    throw new Error('复制知识库失败');
  }
}

/**
 * 检查知识库名称是否可用
 */
export async function checkKnowledgeBaseName(name: string) {
  try {
    const response = await getKnowledgeBaseList({ name });
    if (response.data && response.code === 0) {
      return Array.isArray(response.data) ? response.data.length === 0 : true;
    }
    return false;
  } catch (error) {
    return false;
  }
}

/**
 * 获取知识库使用的嵌入模型列表
 */
export function getEmbeddingModels() {
  // 这里返回常用的嵌入模型列表
  return Promise.resolve([
    { label: 'BAAI/bge-large-zh-v1.5', value: 'BAAI/bge-large-zh-v1.5@BAAI' },
    { label: 'BAAI/bge-base-zh-v1.5', value: 'BAAI/bge-base-zh-v1.5@BAAI' },
    { label: 'BAAI/bge-small-zh-v1.5', value: 'BAAI/bge-small-zh-v1.5@BAAI' },
    { label: 'text-embedding-ada-002', value: 'text-embedding-ada-002@OpenAI' },
    { label: 'text-embedding-3-small', value: 'text-embedding-3-small@OpenAI' },
    { label: 'text-embedding-3-large', value: 'text-embedding-3-large@OpenAI' }
  ]);
}

/**
 * 获取分块方法列表
 */
export function getChunkMethods() {
  return Promise.resolve([
    { label: '通用', value: 'naive' },
    { label: '书籍', value: 'book' },
    { label: '邮件', value: 'email' },
    { label: '法律', value: 'laws' },
    { label: '手动', value: 'manual' },
    { label: '单一', value: 'one' },
    { label: '论文', value: 'paper' },
    { label: '图片', value: 'picture' },
    { label: '演示文稿', value: 'presentation' },
    { label: '问答', value: 'qa' },
    { label: '表格', value: 'table' },
    { label: '标签', value: 'tag' }
  ]);
}
