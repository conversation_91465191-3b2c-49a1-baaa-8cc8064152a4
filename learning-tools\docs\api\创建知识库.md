ragflow-c2ZmY3MzVhMDkwYzExZjA5MzIwZjZjZW
这个是apikey
http://*************:6610/




Create dataset
POST /api/v1/datasets

Creates a dataset.

Request
Method: POST
URL: /api/v1/datasets
Headers:
'content-Type: application/json'
'Authorization: Bearer <YOUR_API_KEY>'
Body:
"name": string
"avatar": string
"description": string
"embedding_model": string
"permission": string
"chunk_method": string
"pagerank": int
"parser_config": object
Request example
curl --request POST \
     --url http://{address}/api/v1/datasets \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '{
      "name": "test_1"
      }'
Request parameters
"name": (Body parameter), string, Required
The unique name of the dataset to create. It must adhere to the following requirements:

Basic Multilingual Plane (BMP) only
Maximum 128 characters
Case-insensitive
"avatar": (Body parameter), string
Base64 encoding of the avatar.

Maximum 65535 characters
"description": (Body parameter), string
A brief description of the dataset to create.

Maximum 65535 characters
"embedding_model": (Body parameter), string
The name of the embedding model to use. For example: "BAAI/bge-large-zh-v1.5@BAAI"

Maximum 255 characters
Must follow model_name@model_factory format
"permission": (Body parameter), string
Specifies who can access the dataset to create. Available options:

"me": (Default) Only you can manage the dataset.
"team": All team members can manage the dataset.
"pagerank": (Body parameter), int
refer to Set page rank

Default: 0
Minimum: 0
Maximum: 100
"chunk_method": (Body parameter), enum<string>
The chunking method of the dataset to create. Available options:

"naive": General (default)
"book": Book
"email": Email
"laws": Laws
"manual": Manual
"one": One
"paper": Paper
"picture": Picture
"presentation": Presentation
"qa": Q&A
"table": Table
"tag": Tag
"parser_config": (Body parameter), object
The configuration settings for the dataset parser. The attributes in this JSON object vary with the selected "chunk_method":

If "chunk_method" is "naive", the "parser_config" object contains the following attributes:
"auto_keywords": int
Defaults to 0
Minimum: 0
Maximum: 32
"auto_questions": int
Defaults to 0
Minimum: 0
Maximum: 10
"chunk_token_num": int
Defaults to 128
Minimum: 1
Maximum: 2048
"delimiter": string
Defaults to "\n".
"html4excel": bool Indicates whether to convert Excel documents into HTML format.
Defaults to false
"layout_recognize": string
Defaults to DeepDOC
"tag_kb_ids": array<string> refer to Use tag set
Must include a list of dataset IDs, where each dataset is parsed using the ​​Tag Chunk Method
"task_page_size": int For PDF only.
Defaults to 12
Minimum: 1
"raptor": object RAPTOR-specific settings.
Defaults to: {"use_raptor": false}
"graphrag": object GRAPHRAG-specific settings.
Defaults to: {"use_graphrag": false}
If "chunk_method" is "qa", "manuel", "paper", "book", "laws", or "presentation", the "parser_config" object contains the following attribute:
"raptor": object RAPTOR-specific settings.
Defaults to: {"use_raptor": false}.
If "chunk_method" is "table", "picture", "one", or "email", "parser_config" is an empty JSON object.
Response
Success:

{
    "code": 0,
    "data": {
        "avatar": null,
        "chunk_count": 0,
        "chunk_method": "naive",
        "create_date": "Mon, 28 Apr 2025 18:40:41 GMT",
        "create_time": 1745836841611,
        "created_by": "3af81804241d11f0a6a79f24fc270c7f",
        "description": null,
        "document_count": 0,
        "embedding_model": "BAAI/bge-large-zh-v1.5@BAAI",
        "id": "3b4de7d4241d11f0a6a79f24fc270c7f",
        "language": "English",
        "name": "RAGFlow example",
        "pagerank": 0,
        "parser_config": {
            "chunk_token_num": 128, 
            "delimiter": "\\n!?;。；！？", 
            "html4excel": false, 
            "layout_recognize": "DeepDOC", 
            "raptor": {
                "use_raptor": false
                }
            },
        "permission": "me",
        "similarity_threshold": 0.2,
        "status": "1",
        "tenant_id": "3af81804241d11f0a6a79f24fc270c7f",
        "token_num": 0,
        "update_date": "Mon, 28 Apr 2025 18:40:41 GMT",
        "update_time": 1745836841611,
        "vector_similarity_weight": 0.3,
    },
}
Failure:

{
    "code": 101,
    "message": "Dataset name 'RAGFlow example' already exists"
}
Delete datasets
DELETE /api/v1/datasets

Deletes datasets by ID.

Request
Method: DELETE
URL: /api/v1/datasets
Headers:
'content-Type: application/json'
'Authorization: Bearer <YOUR_API_KEY>'
Body:
"ids": list[string] or null
Request example
curl --request DELETE \
     --url http://{address}/api/v1/datasets \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '{
     "ids": ["d94a8dc02c9711f0930f7fbc369eab6d", "e94a8dc02c9711f0930f7fbc369eab6e"]
     }'
Request parameters
"ids": (Body parameter), list[string] or null, Required
Specifies the datasets to delete:
If null, all datasets will be deleted.
If an array of IDs, only the specified datasets will be deleted.
If an empty array, no datasets will be deleted.
Response
Success:

{
    "code": 0 
}
Failure:

{
    "code": 102,
    "message": "You don't own the dataset."
}
Update dataset
PUT /api/v1/datasets/{dataset_id}

Updates configurations for a specified dataset.

Request
Method: PUT
URL: /api/v1/datasets/{dataset_id}
Headers:
'content-Type: application/json'
'Authorization: Bearer <YOUR_API_KEY>'
Body:
"name": string
"avatar": string
"description": string
"embedding_model": string
"permission": string
"chunk_method": string
"pagerank": int
"parser_config": object
Request example
curl --request PUT \
     --url http://{address}/api/v1/datasets/{dataset_id} \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "name": "updated_dataset"
     }'
Request parameters
dataset_id: (Path parameter)
The ID of the dataset to update.
"name": (Body parameter), string
The revised name of the dataset.
Basic Multilingual Plane (BMP) only
Maximum 128 characters
Case-insensitive
"avatar": (Body parameter), string
The updated base64 encoding of the avatar.
Maximum 65535 characters
"embedding_model": (Body parameter), string
The updated embedding model name.
Ensure that "chunk_count" is 0 before updating "embedding_model".
Maximum 255 characters
Must follow model_name@model_factory format
"permission": (Body parameter), string
The updated dataset permission. Available options:
"me": (Default) Only you can manage the dataset.
"team": All team members can manage the dataset.
"pagerank": (Body parameter), int
refer to Set page rank
Default: 0
Minimum: 0
Maximum: 100
"chunk_method": (Body parameter), enum<string>
The chunking method for the dataset. Available options:
"naive": General (default)
"book": Book
"email": Email
"laws": Laws
"manual": Manual
"one": One
"paper": Paper
"picture": Picture
"presentation": Presentation
"qa": Q&A
"table": Table
"tag": Tag
"parser_config": (Body parameter), object
The configuration settings for the dataset parser. The attributes in this JSON object vary with the selected "chunk_method":
If "chunk_method" is "naive", the "parser_config" object contains the following attributes:
"auto_keywords": int
Defaults to 0
Minimum: 0
Maximum: 32
"auto_questions": int
Defaults to 0
Minimum: 0
Maximum: 10
"chunk_token_num": int
Defaults to 128
Minimum: 1
Maximum: 2048
"delimiter": string
Defaults to "\n".
"html4excel": bool Indicates whether to convert Excel documents into HTML format.
Defaults to false
"layout_recognize": string
Defaults to DeepDOC
"tag_kb_ids": array<string> refer to Use tag set
Must include a list of dataset IDs, where each dataset is parsed using the ​​Tag Chunk Method
"task_page_size": int For PDF only.
Defaults to 12
Minimum: 1
"raptor": object RAPTOR-specific settings.
Defaults to: {"use_raptor": false}
"graphrag": object GRAPHRAG-specific settings.
Defaults to: {"use_graphrag": false}
If "chunk_method" is "qa", "manuel", "paper", "book", "laws", or "presentation", the "parser_config" object contains the following attribute:
"raptor": object RAPTOR-specific settings.
Defaults to: {"use_raptor": false}.
If "chunk_method" is "table", "picture", "one", or "email", "parser_config" is an empty JSON object.
Response
Success:

{
    "code": 0 
}
Failure:

{
    "code": 102,
    "message": "Can't change tenant_id."
}
List datasets
GET /api/v1/datasets?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={dataset_name}&id={dataset_id}

Lists datasets.

Request
Method: GET
URL: /api/v1/datasets?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={dataset_name}&id={dataset_id}
Headers:
'Authorization: Bearer <YOUR_API_KEY>'
Request example
curl --request GET \
     --url http://{address}/api/v1/datasets?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={dataset_name}&id={dataset_id} \
     --header 'Authorization: Bearer <YOUR_API_KEY>'
Request parameters
page: (Filter parameter)
Specifies the page on which the datasets will be displayed. Defaults to 1.
page_size: (Filter parameter)
The number of datasets on each page. Defaults to 30.
orderby: (Filter parameter)
The field by which datasets should be sorted. Available options:
create_time (default)
update_time
desc: (Filter parameter)
Indicates whether the retrieved datasets should be sorted in descending order. Defaults to true.
name: (Filter parameter)
The name of the dataset to retrieve.
id: (Filter parameter)
The ID of the dataset to retrieve.
Response
Success:

{
    "code": 0,
    "data": [
        {
            "avatar": "",
            "chunk_count": 59,
            "create_date": "Sat, 14 Sep 2024 01:12:37 GMT",
            "create_time": 1726276357324,
            "created_by": "69736c5e723611efb51b0242ac120007",
            "description": null,
            "document_count": 1,
            "embedding_model": "BAAI/bge-large-zh-v1.5",
            "id": "6e211ee0723611efa10a0242ac120007",
            "language": "English",
            "name": "mysql",
            "chunk_method": "naive",
            "parser_config": {
                "chunk_token_num": 8192,
                "delimiter": "\\n",
                "entity_types": [
                    "organization",
                    "person",
                    "location",
                    "event",
                    "time"
                ]
            },
            "permission": "me",
            "similarity_threshold": 0.2,
            "status": "1",
            "tenant_id": "69736c5e723611efb51b0242ac120007",
            "token_num": 12744,
            "update_date": "Thu, 10 Oct 2024 04:07:23 GMT",
            "update_time": 1728533243536,
            "vector_similarity_weight": 0.3
        }
    ]
}
Failure:

{
    "code": 102,
    "message": "The dataset doesn't exist"
}